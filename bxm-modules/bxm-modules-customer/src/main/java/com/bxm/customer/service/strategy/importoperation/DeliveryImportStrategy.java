package com.bxm.customer.service.strategy.importoperation;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationRequest;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationResult;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.DeliveryImportExcelDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 交付导入策略实现
 *
 * 处理交付操作的导入逻辑：
 * 1. 验证交付单状态必须为"已提交待交付"
 * 2. 修改状态为"已交付待确认"
 * 3. 保存附件文件到value_added_file表
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class DeliveryImportStrategy implements ImportOperationStrategy {

    @Autowired
    private IValueAddedDeliveryOrderService deliveryOrderService;

    @Autowired
    private IValueAddedFileService fileService;

    @Override
    public ValueAddedBatchImportOperationType getSupportedOperationType() {
        return ValueAddedBatchImportOperationType.DELIVERY;
    }

    @Override
    public BatchImportOperationResult executeImport(
            List<ValueAddedDeliveryOrder> orders,
            BatchImportOperationRequest request,
            Map<String, Object> templateData,
            Map<String, String> extractedFiles) {

        log.info("Starting delivery import operation, order count: {}", orders.size());

        LocalDateTime startTime = LocalDateTime.now();
        List<String> successOrderNos = new ArrayList<>();
        List<BatchOperationErrorDTO> errors = new ArrayList<>();

        // 验证模板数据
        try {
            validateTemplateData(templateData, request.getDeliveryOrderNoList());
        } catch (Exception e) {
            log.error("Template data validation failed: {}", e.getMessage());
            throw new IllegalArgumentException("Template data validation failed: " + e.getMessage());
        }

        // 处理每个交付单
        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 验证交付单状态
                validateOrderStatus(order);

                // 修改状态为已交付待确认
                String targetStatus = getTargetStatus(order.getStatus());
                if (targetStatus != null) {
                    StatusChangeRequestDTO changeRequest = StatusChangeRequestDTO.builder()
                            .deliveryOrderNo(order.getDeliveryOrderNo())
                            .targetStatus(targetStatus)
                            .reason("批量交付导入")
                            .operatorId(SecurityUtils.getUserId())
                            .operatorName(SecurityUtils.getUsername())
                            .remark("批量交付导入操作")
                            .build();

                    deliveryOrderService.changeStatus(changeRequest);
                    log.info("Delivery order status changed successfully: {} -> {}", order.getDeliveryOrderNo(), targetStatus);
                }

                // 处理文件保存
                if (extractedFiles != null && !extractedFiles.isEmpty()) {
                    int savedFileCount = processFileSaving(order, extractedFiles, request);
                    log.info("Delivery order {} saved file count: {}", order.getDeliveryOrderNo(), savedFileCount);
                }

                successOrderNos.add(order.getDeliveryOrderNo());

            } catch (Exception e) {
                log.warn("Delivery order {} processing failed: {}", order.getDeliveryOrderNo(), e.getMessage());
                errors.add(BatchOperationErrorDTO.builder()
                        .deliveryOrderNo(order.getDeliveryOrderNo())
                        .customerName(order.getCustomerName())
                        .errorInfo(e.getMessage())
                        .build());
            }
        }

        LocalDateTime endTime = LocalDateTime.now();
        long processingTime = java.time.Duration.between(startTime, endTime).toMillis();

        return BatchImportOperationResult.builder()
                .operationDescription(getOperationDescription())
                .totalCount(orders.size())
                .successCount(successOrderNos.size())
                .errorCount(errors.size())
                .successOrderNos(successOrderNos)
                .errors(errors)
                .startTime(startTime)
                .endTime(endTime)
                .processingTimeMs(processingTime)
                .build();
    }

    @Override
    public void validateOrderStatus(ValueAddedDeliveryOrder order) {
        String currentStatus = order.getStatus();
        if (!ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(currentStatus)) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status does not allow delivery operation, current status: %s, required status: %s",
                            order.getDeliveryOrderNo(),
                            ValueAddedDeliveryOrderStatus.getByCode(currentStatus).getDescription(),
                            ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getDescription())
            );
        }
    }

    @Override
    public String getTargetStatus(String currentStatus) {
        // 交付操作：已提交待交付 -> 已交付待确认
        if (ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(currentStatus)) {
            return ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode();
        }
        return null;
    }

    @Override
    public void validateTemplateData(Map<String, Object> templateData, List<String> orderNos) {
        // Validate template data consistency with request parameters
        if (templateData == null || templateData.isEmpty()) {
            throw new IllegalArgumentException("Template data cannot be empty");
        }

        // Here can validate according to actual Excel template structure
        // For example, validate delivery result, total deduction amount and other fields
        log.info("Delivery template data validation passed, contains {} delivery orders", orderNos.size());
    }

    @Override
    public int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationRequest request) {

        int savedCount = 0;
        String deliveryOrderNo = order.getDeliveryOrderNo();

        for (Map.Entry<String, String> entry : extractedFiles.entrySet()) {
            try {
                String fileName = entry.getKey();
                String filePath = entry.getValue();

                // Check if file name is related to delivery order number
                if (isFileRelatedToOrder(fileName, deliveryOrderNo)) {
                    ValueAddedFile file = new ValueAddedFile();
                    file.setDeliveryOrderNo(deliveryOrderNo);
                    file.setFileName(fileName);
                    file.setFileUrl(filePath);
                    file.setFileType(1); // 1-交付材料附件
                    file.setStatus(1); // 1-处理完成
                    file.setIsDel(false);
                    file.setRemark("批量交付导入");
                    file.setCreateBy(SecurityUtils.getUserId().toString());

                    boolean saved = fileService.save(file);
                    if (saved) {
                        savedCount++;
                        log.debug("File saved successfully: {} -> {}", fileName, deliveryOrderNo);
                    }
                }
            } catch (Exception e) {
                log.warn("File save failed: {}, error: {}", entry.getKey(), e.getMessage());
            }
        }

        return savedCount;
    }

    /**
     * 判断文件是否与交付单相关
     * 根据文件名包含交付单编号或其他规则判断
     */
    private boolean isFileRelatedToOrder(String fileName, String deliveryOrderNo) {
        if (fileName == null || deliveryOrderNo == null) {
            return false;
        }

        // 简单的匹配规则：文件名包含交付单编号
        return fileName.toUpperCase().contains(deliveryOrderNo.toUpperCase());
    }

    @Override
    public List<? extends BaseImportExcelDTO> parseTemplateFile(MultipartFile templateFile) throws Exception {
        log.info("开始解析交付操作Excel模板文件: {}", templateFile.getOriginalFilename());

        try {
            // 使用ExcelUtil解析Excel文件为DeliveryImportExcelDTO列表
            ExcelUtil<DeliveryImportExcelDTO> excelUtil = new ExcelUtil<>(DeliveryImportExcelDTO.class);
            List<DeliveryImportExcelDTO> dataList = excelUtil.importExcel(templateFile.getInputStream());

            // 设置行号用于错误定位
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setRowNumber(i + 2); // Excel从第2行开始是数据行
            }

            log.info("交付操作Excel模板文件解析完成，共解析{}条记录", dataList.size());
            return dataList;

        } catch (Exception e) {
            log.error("交付操作Excel模板文件解析失败: {}", e.getMessage(), e);
            throw new Exception("交付操作Excel模板文件解析失败: " + e.getMessage(), e);
        }
    }
}
